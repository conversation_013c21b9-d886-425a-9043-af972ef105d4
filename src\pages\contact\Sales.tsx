import React, { useRef, useState } from "react";
import { motion, useInView } from "framer-motion";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import {
  Mail,
  Phone,
  MapPin,
  Navigation,
  Send,
  Building,
  User,
  MessageSquare,
  Briefcase,
  Bookmark,
  MapPinned,
  FileText
} from "lucide-react";

const Sales = () => {
  const contactRef = useRef(null);
  const contactInView = useInView(contactRef, { amount: 0.1, once: true });



  // State for form inputs to handle floating labels
  const [formInputs, setFormInputs] = useState({
    name: '',
    email: '',
    company: '',
    designation: '',
    city: '',
    mobile: '',
    pincode: '',
    products: '',
    remarks: ''
  });

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setFormInputs(prev => ({
      ...prev,
      [id]: value
    }));
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  // Function to handle map click
  const handleMapClick = () => {
    const address = "No.5, Kumaran St, Pazhvanthangal, Chennai, Tamil Nadu, India, 600114";
    const encodedAddress = encodeURIComponent(address);
    window.open(`https://www.google.com/maps/search/?api=1&query=${encodedAddress}`, '_blank');
  };

  return (
    <PageLayout
      title="SALES DEPARTMENT"
      subtitle="Get in touch with our sales team for product information and tailored solutions"
      category="contact"
    >
      <div className="max-w-7xl mx-auto px-2 sm:px-4 py-8 sm:py-12 relative overflow-hidden">
        {/* Simplified background decorative elements */}
        <div className="absolute top-0 right-0 w-40 sm:w-64 h-40 sm:h-64 bg-blue-500/5 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-60 sm:w-96 h-60 sm:h-96 bg-green-500/5 rounded-full filter blur-3xl"></div>
        <div
          id="contact"
          ref={contactRef}
          className="relative"
        >
          {/* Animated background elements - subtle and non-intrusive */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(4)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full opacity-5"
                style={{
                  width: 80 + Math.random() * 120, // smaller on mobile
                  height: 80 + Math.random() * 120,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  background: i % 3 === 0
                    ? "radial-gradient(circle, rgba(59, 130, 246, 0.6) 0%, rgba(29, 78, 216, 0) 70%)"
                    : i % 3 === 1
                      ? "radial-gradient(circle, rgba(16, 185, 129, 0.6) 0%, rgba(4, 120, 87, 0) 70%)"
                      : "radial-gradient(circle, rgba(245, 158, 11, 0.6) 0%, rgba(180, 83, 9, 0) 70%)",
                }}
                animate={{
                  scale: [1, 1.1, 1],
                  opacity: [0.05, 0.08, 0.05],
                }}
                transition={{
                  duration: 12 + Math.random() * 10,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={contactInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8 sm:mb-10"
          >
            <span className="inline-block bg-gradient-to-r from-blue-700 via-green-700 to-yellow-700 text-white px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-medium mb-2 sm:mb-3 border border-white/20 shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-default">
              Get In Touch
            </span>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 text-black drop-shadow-sm">
              Let's Discuss Your <span className="text-black">Energy Needs</span>
            </h2>
            <p className="text-base sm:text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
              Our team of specialists is ready to help you find the perfect solution for your energy management challenges.
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={contactInView ? "visible" : "hidden"}
            className="grid grid-cols-1 lg:grid-cols-12 gap-6 sm:gap-10 items-start"
          >
            {/* Form Section - 7 columns on large screens */}
            <motion.div
              variants={itemVariants}
              className="lg:col-span-7 transition-all duration-700 hover:scale-[1.02]"
            >
              <div className="p-6 sm:p-8 bg-white rounded-2xl shadow-2xl border border-gray-100 hover:border-gray-200 hover:shadow-3xl transition-all duration-300 relative overflow-hidden">
                {/* Modern decorative elements */}
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-50 rounded-full opacity-50"></div>
                <div className="absolute -bottom-4 -left-4 w-20 h-20 bg-gradient-to-tr from-gray-50 to-gray-100 rounded-full opacity-30"></div>
                <div className="relative z-10">
                  <h3 className="text-3xl font-bold mb-8 text-black flex items-center gap-x-3">
                    <div className="p-2 bg-black rounded-lg">
                      <MessageSquare className="h-6 w-6 text-white" />
                    </div>
                    Get in Touch
                  </h3>
                  <form className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Name Field */}
                      <div className="space-y-2">
                        <label htmlFor="name" className="block text-sm font-semibold text-black mb-2">
                          Full Name *
                        </label>
                        <div className="relative group">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <User className="h-5 w-5 text-gray-400 group-focus-within:text-black transition-colors duration-200" />
                          </div>
                          <input
                            type="text"
                            id="name"
                            value={formInputs.name}
                            onChange={handleInputChange}
                            className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-black focus:bg-white transition-all duration-200 hover:border-gray-300"
                            placeholder="Enter your full name"
                          />
                        </div>
                      </div>
                      {/* Email Field */}
                      <div className="space-y-2">
                        <label htmlFor="email" className="block text-sm font-semibold text-black mb-2">
                          Email Address *
                        </label>
                        <div className="relative group">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <Mail className="h-5 w-5 text-gray-400 group-focus-within:text-black transition-colors duration-200" />
                          </div>
                          <input
                            type="email"
                            id="email"
                            value={formInputs.email}
                            onChange={handleInputChange}
                            className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-black focus:bg-white transition-all duration-200 hover:border-gray-300"
                            placeholder="Enter your email address"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label htmlFor="company" className="block text-sm font-semibold text-black mb-2">
                          Company *
                        </label>
                        <div className="relative group">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <Building className="h-5 w-5 text-gray-400 group-focus-within:text-black transition-colors duration-200" />
                          </div>
                          <input
                            type="text"
                            id="company"
                            value={formInputs.company}
                            onChange={handleInputChange}
                            className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-black focus:bg-white transition-all duration-200 hover:border-gray-300"
                            placeholder="Enter your company name"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="designation" className="block text-sm font-semibold text-black mb-2">
                          Job Title *
                        </label>
                        <div className="relative group">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <Briefcase className="h-5 w-5 text-gray-400 group-focus-within:text-black transition-colors duration-200" />
                          </div>
                          <input
                            type="text"
                            id="designation"
                            value={formInputs.designation}
                            onChange={handleInputChange}
                            className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-black focus:bg-white transition-all duration-200 hover:border-gray-300"
                            placeholder="Enter your job title"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label htmlFor="city" className="block text-sm font-semibold text-black mb-2">
                          City *
                        </label>
                        <div className="relative group">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <MapPinned className="h-5 w-5 text-gray-400 group-focus-within:text-black transition-colors duration-200" />
                          </div>
                          <input
                            type="text"
                            id="city"
                            value={formInputs.city}
                            onChange={handleInputChange}
                            className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-black focus:bg-white transition-all duration-200 hover:border-gray-300"
                            placeholder="Enter your city"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="mobile" className="block text-sm font-semibold text-black mb-2">
                          Phone Number *
                        </label>
                        <div className="relative group">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <Phone className="h-5 w-5 text-gray-400 group-focus-within:text-black transition-colors duration-200" />
                          </div>
                          <input
                            type="tel"
                            id="mobile"
                            value={formInputs.mobile}
                            onChange={handleInputChange}
                            className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-black focus:bg-white transition-all duration-200 hover:border-gray-300"
                            placeholder="Enter your phone number"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label htmlFor="pincode" className="block text-sm font-semibold text-black mb-2">
                          Postal Code *
                        </label>
                        <div className="relative group">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <MapPin className="h-5 w-5 text-gray-400 group-focus-within:text-black transition-colors duration-200" />
                          </div>
                          <input
                            type="text"
                            id="pincode"
                            value={formInputs.pincode}
                            onChange={handleInputChange}
                            className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-black focus:bg-white transition-all duration-200 hover:border-gray-300"
                            placeholder="Enter postal code"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="products" className="block text-sm font-semibold text-black mb-2">
                          Product Interest *
                        </label>
                        <div className="relative group">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <Bookmark className="h-5 w-5 text-gray-400 group-focus-within:text-black transition-colors duration-200" />
                          </div>
                          <select
                            id="products"
                            value={formInputs.products}
                            onChange={handleInputChange}
                            className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black focus:outline-none focus:ring-0 focus:border-black focus:bg-white appearance-none transition-all duration-200 hover:border-gray-300"
                          >
                            <option value="">Select a product</option>
                            <option value="measure">Measurement Solutions</option>
                            <option value="protect">Protection Systems</option>
                            <option value="conserve">Conservation Technologies</option>
                            <option value="consultation">Energy Consultation</option>
                          </select>
                          <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="remarks" className="block text-sm font-semibold text-black mb-2">
                        Additional Requirements
                      </label>
                      <div className="relative group">
                        <div className="absolute top-4 left-4 pointer-events-none">
                          <MessageSquare className="h-5 w-5 text-gray-400 group-focus-within:text-black transition-colors duration-200" />
                        </div>
                        <textarea
                          id="remarks"
                          rows={4}
                          value={formInputs.remarks}
                          onChange={handleInputChange}
                          className="w-full pl-12 pr-4 py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-black focus:bg-white transition-all duration-200 hover:border-gray-300 resize-none"
                          placeholder="Tell us about your specific requirements or questions..."
                        ></textarea>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="text-lg font-semibold text-black mb-4">How can we help you?</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                          <input
                            type="checkbox"
                            id="request-demo"
                            className="h-5 w-5 text-black focus:ring-black border-gray-300 rounded cursor-pointer"
                          />
                          <label htmlFor="request-demo" className="ml-3 flex items-center gap-x-2 text-sm text-black font-medium cursor-pointer">
                            <Send className="h-4 w-4 text-black" />
                            Request Demo
                          </label>
                        </div>
                        <div className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                          <input
                            type="checkbox"
                            id="request-callback"
                            className="h-5 w-5 text-black focus:ring-black border-gray-300 rounded cursor-pointer"
                          />
                          <label htmlFor="request-callback" className="ml-3 flex items-center gap-x-2 text-sm text-black font-medium cursor-pointer">
                            <Phone className="h-4 w-4 text-black" />
                            Request Call Back
                          </label>
                        </div>
                        <div className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                          <input
                            type="checkbox"
                            id="send-details"
                            className="h-5 w-5 text-black focus:ring-black border-gray-300 rounded cursor-pointer"
                          />
                          <label htmlFor="send-details" className="ml-3 flex items-center gap-x-2 text-sm text-black font-medium cursor-pointer">
                            <FileText className="h-4 w-4 text-black" />
                            Send Product Details
                          </label>
                        </div>
                        <div className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                          <input
                            type="checkbox"
                            id="send-updates"
                            className="h-5 w-5 text-black focus:ring-black border-gray-300 rounded cursor-pointer"
                          />
                          <label htmlFor="send-updates" className="ml-3 flex items-center gap-x-2 text-sm text-black font-medium cursor-pointer">
                            <Bookmark className="h-4 w-4 text-black" />
                            Stay Updated
                          </label>
                        </div>
                      </div>
                    </div>

                    {/* Submit Button */}
                    <motion.div
                      className="pt-6"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        type="submit"
                        className="w-full flex items-center justify-center
                                   px-8 py-5
                                   text-lg
                                   font-bold
                                   text-white
                                   bg-gradient-to-r from-black to-gray-800
                                   hover:from-gray-800 hover:to-black
                                   rounded-2xl
                                   transition-all duration-300
                                   shadow-2xl
                                   hover:shadow-3xl
                                   focus:outline-none
                                   focus:ring-4
                                   focus:ring-black/20
                                   focus:ring-offset-2
                                   transform hover:-translate-y-1
                                   active:translate-y-0"
                      >
                        <Send className="h-6 w-6 mr-3" />
                        Send Enquiry
                      </Button>
                    </motion.div>
                  </form>
                  </div>
                </div>
            </motion.div>

            {/* Contact Info Section - 5 columns on large screens */}
            <motion.div
              variants={itemVariants}
              className="lg:col-span-5 space-y-4 sm:space-y-6"
            >
              {/* Main Contact Info */}
              <div className="transition-all duration-700 hover:scale-[1.02] p-4 sm:p-8 bg-gradient-to-br from-white/70 to-white/95 backdrop-blur-md rounded-xl shadow-xl border border-blue-100/50 hover:border-blue-200 hover:shadow-blue-100/30 relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-500/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>
                <div>
                  {/* Header with shine effect */}
                  <div className="relative mb-6 overflow-hidden rounded-lg bg-gradient-to-r from-blue-50/50 to-transparent">
                    <h3 className="text-xl font-bold text-black mb-4 py-3 px-2 sm:px-4 drop-shadow-sm flex items-center gap-x-2">
                      <Mail className="h-5 w-5 text-black" />
                      Atandra Energy Pvt.Ltd.
                    </h3>

                    {/* Enhanced shine effect */}
                    <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent transform -skew-x-20 animate-shine"></div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                    <div className="sm:col-span-2 col-span-1">
                      {/* Address row: icon, address, view map (all in a row on mobile) */}
                      <div className="flex flex-row items-center gap-x-2 mb-2">
                        <MapPin className="h-5 w-5 text-green-600 flex-shrink-0" />
                        <div className="flex flex-col text-left">
                          <p className="text-black leading-tight">No.5, Kumaran St,</p>
                          <p className="text-black leading-tight">Pazhvanthangal, Tamil Nadu,</p>
                          <p className="text-black leading-tight">India, Chennai- 600 114.</p>
                        </div>
                        <motion.div
                          onClick={handleMapClick}
                          className="ml-2 flex flex-col items-center justify-center cursor-pointer transition-colors bg-green-50/50 rounded-lg hover:bg-green-50 px-3 py-2 w-fit"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.98 }}
                          style={{ minWidth: '90px' }}
                        >
                          <Navigation className="h-6 w-6 text-green-600 mb-1" />
                          <span className="text-black text-xs font-medium">View Map</span>
                        </motion.div>
                      </div>
                    </div>
                  </div>


                </div>
              </div>

              {/* India Service Locations Map - Fully Responsive */}
              <motion.div
                variants={itemVariants}
                className="w-full"
              >
                <div className="bg-gradient-to-br from-white/90 to-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-gray-100/50 hover:border-gray-200 hover:shadow-3xl transition-all duration-500 overflow-hidden">
                  {/* Map Header */}
                  <div className="px-4 sm:px-6 lg:px-8 py-6 bg-gradient-to-r from-blue-50/50 to-green-50/50 border-b border-gray-100">
                    <div className="text-center">
                      <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-black mb-2 flex items-center justify-center gap-x-3">
                        <div className="p-2 bg-gradient-to-r from-blue-600 to-green-600 rounded-lg">
                          <MapPin className="h-6 w-6 text-white" />
                        </div>
                        Our Service Locations Across India
                      </h3>
                      <p className="text-sm sm:text-base text-gray-700 max-w-2xl mx-auto">
                        Comprehensive coverage nationwide with dedicated service centers and support teams
                      </p>
                    </div>
                  </div>

                  {/* India Map Image - Responsive */}
                  <div className="p-4 sm:p-6 lg:p-8">
                    <div className="relative w-full max-w-4xl mx-auto">
                      <div className="overflow-hidden rounded-xl shadow-xl relative group bg-white">
                        <img
                          src="/background_images/Service-Locations-India.jpeg"
                          alt="KRYKARD Service Locations Across India"
                          className="w-full h-auto object-contain transition-transform duration-700 group-hover:scale-105"
                          style={{
                            minHeight: '250px',
                            maxHeight: '600px',
                            objectFit: 'contain'
                          }}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-blue-900/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>

                      {/* Service Coverage Statistics */}
                      <div className="mt-6 sm:mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-blue-50 rounded-xl border border-blue-100 hover:bg-blue-100 transition-colors duration-200">
                          <div className="text-2xl sm:text-3xl font-bold text-blue-600 mb-1">25+</div>
                          <div className="text-sm text-blue-800 font-medium">States Covered</div>
                        </div>
                        <div className="text-center p-4 bg-green-50 rounded-xl border border-green-100 hover:bg-green-100 transition-colors duration-200">
                          <div className="text-2xl sm:text-3xl font-bold text-green-600 mb-1">100+</div>
                          <div className="text-sm text-green-800 font-medium">Service Centers</div>
                        </div>
                        <div className="text-center p-4 bg-yellow-50 rounded-xl border border-yellow-100 hover:bg-yellow-100 transition-colors duration-200 sm:col-span-2 lg:col-span-1">
                          <div className="text-2xl sm:text-3xl font-bold text-yellow-600 mb-1">24/7</div>
                          <div className="text-sm text-yellow-800 font-medium">Support Available</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

            </motion.div>
          </motion.div>

          {/* Google Maps Location - KRYKARD Headquarters */}
          <motion.div
            variants={itemVariants}
            className="w-full mt-10 sm:mt-16"
          >
            <div className="bg-gradient-to-br from-white/90 to-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-gray-100/50 hover:border-gray-200 hover:shadow-3xl transition-all duration-500 overflow-hidden">
              {/* Location Header */}
              <div className="px-4 sm:px-6 lg:px-8 py-6 bg-gradient-to-r from-blue-50/50 to-green-50/50 border-b border-gray-100">
                <div className="text-center">
                  <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-black mb-2 flex items-center justify-center gap-x-3">
                    <div className="p-2 bg-gradient-to-r from-blue-600 to-green-600 rounded-lg">
                      <Building className="h-6 w-6 text-white" />
                    </div>
                    Our Headquarters Location
                  </h3>
                  <p className="text-sm sm:text-base text-gray-700 max-w-2xl mx-auto flex items-center justify-center gap-x-2">
                    <MapPin className="h-4 w-4 text-blue-600" />
                    KRYKARD Headquarters - Chennai, India
                  </p>
                </div>
              </div>

              {/* Google Maps Container */}
              <div className="p-4 sm:p-6 lg:p-8">
                <motion.div
                  className="relative overflow-hidden transition-all duration-500 hover:scale-[1.01] cursor-pointer rounded-xl shadow-xl border border-blue-100/30 group"
                  whileHover={{ scale: 1.02 }}
                  onClick={() => window.open('https://www.google.com/maps/place/KRYKARD/@13.0963814,80.2622446,15z/data=!4m6!3m5!1s0x3a5260aa8721279f:0x44507a3129269ebe!8m2!3d13.0963814!4d80.2622446!16s%2Fg%2F11j7lq_v6h?entry=ttu', '_blank')}
                >
                  <div className="relative">
                    {/* eslint-disable-next-line jsx-a11y/iframe-has-title */}
                    <iframe
                      src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d22938.9286128844!2d80.2622445948994!3d13.096381397513284!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a5260aa8721279f%3A0x44507a3129269ebe!2sKRYKARD!5e1!3m2!1sen!2sus!4v1744869740527!5m2!1sen!2sus"
                      width="100%"
                      height="300"
                      style={{ border: 0 }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      title="KRYKARD Location Map"
                      className="z-10 w-full max-w-full min-h-[250px] sm:min-h-[300px] lg:min-h-[350px] rounded-lg"
                    />
                  </div>

                  {/* Enhanced Directions button */}
                  <div className="absolute bottom-4 right-4 z-30">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="bg-white p-3 flex items-center rounded-full shadow-lg border border-blue-100/50 hover:bg-blue-50 transition-all duration-300"
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open('https://www.google.com/maps/place/KRYKARD/@13.0963814,80.2622446,15z/data=!4m6!3m5!1s0x3a5260aa8721279f:0x44507a3129269ebe!8m2!3d13.0963814!4d80.2622446!16s%2Fg%2F11j7lq_v6h?entry=ttu', '_blank');
                      }}
                    >
                      <Navigation className="h-5 w-5 text-blue-700 mr-2" />
                      <span className="text-blue-800 font-medium">Get Directions</span>
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>

        </div>

        {/* Add the animations */}
        <style>{`
          @keyframes shine {
            0% {
              left: -100%;
            }
            100% {
              left: 100%;
            }
          }

          @keyframes pulse {
            0%, 100% {
              opacity: 0.2;
            }
            50% {
              opacity: 0.5;
            }
          }

          @keyframes ping {
            0% {
              transform: scale(1);
              opacity: 0.8;
            }
            75%, 100% {
              transform: scale(1.5);
              opacity: 0;
            }
          }

          .animate-shine {
            animation: shine 3s infinite;
          }

          .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
          }

          .animate-ping {
            animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
          }

          /* Button glow animation */
          @keyframes glow {
            0%, 100% {
              box-shadow: 0 0 5px rgba(59, 130, 246, 0.5), 0 0 20px rgba(59, 130, 246, 0.2);
            }
            50% {
              box-shadow: 0 0 15px rgba(59, 130, 246, 0.8), 0 0 40px rgba(59, 130, 246, 0.4);
            }
          }

          .animate-glow {
            animation: glow 2s infinite;
          }

          /* Floating animation for cards - simplified for performance */
          .animate-float {
            /* animation removed for performance */
          }

          /* Subtle breathing animation - removed for performance */
          .animate-breathe {
            /* animation removed for performance */
          }

          /* Button shimmer effect - removed for performance */
          .animate-shimmer {
            /* animation removed for performance */
          }

          /* Gradient shift animation */
          @keyframes gradient-shift {
            0% {
              background-position: 0% 50%;
            }
            50% {
              background-position: 100% 50%;
            }
            100% {
              background-position: 0% 50%;
            }
          }

          .animate-gradient {
            background-size: 200% 200%;
            animation: gradient-shift 5s ease infinite;
          }
        `}</style>
      </div>
    </PageLayout>
  );
};

export default Sales;