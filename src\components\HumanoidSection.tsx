import React, { useState } from "react";

const cardBaseStyle =
  "rounded-2xl p-8 flex flex-col justify-between shadow-xl min-h-[260px] relative overflow-hidden w-full h-full";
const overlayStyle = {
  backgroundSize: "cover",
  backgroundPosition: "center",
  backgroundBlendMode: "overlay",
};

const cards = [
  {
    key: "vision",
    overlay: { ...overlayStyle, backgroundImage: "url('/background-section1.png')", backgroundPosition: "top center" },
    label: "The vision",
    content: (
      <ul className="list-disc pl-6 space-y-3 text-base md:text-lg font-semibold relative z-10">
        <li>To achieve & sustain leadership positions and be the company of choice in all our areas of business</li>
        <li>To be a great place to work where every employee values the environment & opportunity provided</li>
        <li>To be the preferred partner for all our external stakeholders</li>
      </ul>
    ),
  },
  {
    key: "mission",
    overlay: { ...overlayStyle, backgroundImage: "url('/background-section2.png')", backgroundPosition: "center" },
    label: "The mission",
    content: (
      <ul className="list-disc pl-6 space-y-3 text-base md:text-lg font-semibold relative z-10">
        <li>To be a respected Indian MNC offering innovative solutions in the domain of power conditioning, measurement & conservation of energy and resources for a more sustainable world by offering best-in-class Products and services with a committed & competent team ensuring continual customer satisfaction with involved partners and Suppliers.</li>
      </ul>
    ),
  },
  {
    key: "about",
    overlay: { ...overlayStyle, backgroundImage: "url('/background-section3.png')", backgroundPosition: "bottom center" },
    label: "About Atandra Energy",
    content: (
      <div className="relative z-10">
        <h3 className="text-2xl md:text-3xl font-display font-bold leading-tight mb-3">Leading Power & Energy Management Solutions</h3>
        <p className="text-base md:text-lg text-white/90 leading-relaxed">
          Atandra Energy Pvt. Ltd., headquartered in Chennai, is a leading Indian company specializing in power and energy management solutions under the well-known brand <span className="text-[#FC4D0A] font-semibold">KRYKARD</span>. With over <span className="text-[#FC4D0A] font-semibold">40+ years</span> of industry experience, we deliver innovative solutions for power conditioning, measurement & conservation of energy and resources for a more sustainable world.
        </p>
      </div>
    ),
  },
  {
    key: "stats",
    overlay: { ...overlayStyle, backgroundImage: "url('/background-section1.png')", backgroundPosition: "center", opacity: 0.7 },
    label: "Key Stats",
    content: (
      <>
        <div className="flex flex-col gap-2 mb-4 relative z-10">
          <div className="text-3xl font-bold">40+<span className="text-base font-normal ml-2">years</span></div>
          <div className="text-white/80 text-base">Industry experience</div>
        </div>
        <div className="flex flex-col gap-2 mb-4 relative z-10">
          <div className="text-3xl font-bold">500+<span className="text-base font-normal ml-2">projects</span></div>
          <div className="text-white/80 text-base">Successful projects completed</div>
        </div>
        <div className="flex flex-col gap-2 relative z-10">
          <div className="text-3xl font-bold">238+<span className="text-base font-normal ml-2">brands</span></div>
          <div className="text-white/80 text-base">Brands served worldwide</div>
        </div>
      </>
    ),
  },
];

const HumanoidSection = () => {
  const [activeIndex, setActiveIndex] = useState(0);

  const prevCard = () => setActiveIndex((prev) => (prev === 0 ? cards.length - 1 : prev - 1));
  const nextCard = () => setActiveIndex((prev) => (prev === cards.length - 1 ? 0 : prev + 1));

  return (
    <section className="w-full py-16 bg-white" id="why-humanoid">
      <div className="container mx-auto px-6 lg:px-8 flex flex-col md:flex-row gap-12 items-stretch">
        {/* Left Column */}
        <div className="flex-1 flex flex-col justify-center max-w-lg">
          <div className="flex items-center gap-4 mb-6">
            <span className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-[#D4FF7F] text-[#222] text-lg font-bold">04</span>
            <span className="block w-20 h-px bg-gray-200"></span>
            <button className="px-6 py-2 rounded-full bg-[#222] text-white font-medium text-base shadow">About us</button>
          </div>
          <h2 className="text-4xl md:text-5xl font-display font-bold mb-4 text-[#222]">Why Atandra Energy</h2>
          <p className="text-lg text-gray-600 mb-2">
            We blend creativity with strategy to craft unique solutions in power and energy management, with a focus on innovation and attention to detail.
          </p>
        </div>
        {/* Right Column: Carousel */}
        <div className="flex-1 flex flex-col items-center justify-center relative min-h-[340px]">
          <div className="w-full max-w-xl h-[340px] flex items-center justify-center relative">
            {/* Card Carousel */}
            {cards.map((card, idx) => (
              <div
                key={card.key}
                className={
                  cardBaseStyle +
                  ` absolute top-0 left-0 transition-all duration-500 ease-in-out ${
                    idx === activeIndex ? "opacity-100 scale-100 z-20" : "opacity-0 scale-95 z-10 pointer-events-none"
                  }`
                }
                style={card.overlay}
              >
                <div className="flex items-center mb-3 relative z-10">
                  <span className="inline-flex items-center justify-center px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm text-white text-sm font-medium">{card.label}</span>
                </div>
                {card.content}
              </div>
            ))}
            {/* Navigation Arrows */}
            <button
              onClick={prevCard}
              className="absolute left-0 top-1/2 -translate-y-1/2 bg-white/70 hover:bg-white text-[#222] rounded-full w-10 h-10 flex items-center justify-center shadow z-30"
              aria-label="Previous"
            >
              &#8592;
            </button>
            <button
              onClick={nextCard}
              className="absolute right-0 top-1/2 -translate-y-1/2 bg-white/70 hover:bg-white text-[#222] rounded-full w-10 h-10 flex items-center justify-center shadow z-30"
              aria-label="Next"
            >
              &#8594;
            </button>
          </div>
          {/* Dots */}
          <div className="flex gap-2 mt-6">
            {cards.map((_, idx) => (
              <button
                key={idx}
                onClick={() => setActiveIndex(idx)}
                className={`w-3 h-3 rounded-full ${idx === activeIndex ? "bg-[#FC4D0A]" : "bg-gray-300"}`}
                aria-label={`Go to card ${idx + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HumanoidSection;
